package com.lhx.birthday.service;

import com.lhx.birthday.request.astrolabe.*;
import com.lhx.birthday.vo.Result;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.function.Consumer;

public interface IAstrolabeService {

    /**
     * 基础接口-新闻内容
     * @param articleDetailsRequest
     * @return
     */
    Result articleDetails(ArticleDetailsRequest articleDetailsRequest);

    /**
     * 星盘-本命盘
     * @param natalRequest
     * @return
     */
    Result chartNatal(NatalRequest natalRequest);

    /**
     * 获取本命盘语料
     * @param natalCorpusRequest
     * @return
     */
    Result getNatalCorpus(NatalCorpusRequest natalCorpusRequest);

    /**
     * 星盘-统一接口
     * @param chartRequest
     * @return
     */
    Result getChart(ChartRequest chartRequest);

    /**
     * 星盘-语料统一接口
     * @param chartRequest
     * @return
     */
    Result<JSONObject> getChartCorpus(ChartRequest chartRequest);

    /**
     * 星盘-年运报告
     * @param yearReportRequest
     * @return
     */
    Result report(YearReportRequest yearReportRequest);

    /**
     * 星盘-语料列表
     * @param corpusconstellationRequest
     * @return
     */
    Result corpusconstellationList(CorpusconstellationRequest corpusconstellationRequest);

    /**
     * 星盘语料-语料
     * @param luckRequest
     * @return
     */
    Result luck(LuckRequest luckRequest);

    /**
     * 星盘语料-缘分合盘
     * @param evaluationcombinationRequest
     * @return
     */
    Result evaluationcombination(EvaluationcombinationRequest evaluationcombinationRequest);

    /**
     * 星相日历
     * @param astroCalendarRequest 请求参数
     * @return 星相日历数据
     */
    Result getAstroCalendar(AstroCalendarRequest astroCalendarRequest);

    /**
     * 获取行星逆顺事件对
     * @param planetRetrogradePairsRequest 请求参数
     * @return 行星逆顺事件对数据
     */
    Result getPlanetRetrogradePairs(PlanetRetrogradePairsRequest planetRetrogradePairsRequest);

    /**
     * 推算星座
     * @param signPushRequest
     * @return
     */
    Result signPush(SignPushRequest signPushRequest);

    /**
     * 获取并保存星相日历数据
     * @param year 年份
     * @param month 月份
     */
    void fetchAndSaveAstroCalendar(int year, int month);

    /**
     * 获取本命盘数据
     * @param constellationId 星盘档案ID
     * @return 本命盘数据
     */
    JSONObject getNatalChart(Long constellationId);

    void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter);
    
    /**
     * 发送缓存的分析结果流
     * @param cachedResult 缓存的分析结果
     * @param emitter SSE发射器
     */
    void sendCachedAnalysisStream(JSONObject cachedResult, SseEmitter emitter);
    
    /**
     * 获取DeepSeek分析结果流，并通过回调函数返回完整结果
     * @param chartData 星盘数据
     * @param emitter SSE发射器
     * @param resultCallback 分析完成后的回调函数，用于返回完整结果
     */
    void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter, Consumer<JSONObject> resultCallback);

    /**
     * 获取DeepSeek分析结果流，带增量更新和完整结果回调
     * @param chartData 星盘数据
     * @param emitter SSE发射器
     * @param incrementalCallback 每收到一段内容时的回调，用于实时广播增量更新
     * @param resultCallback 分析完成后的回调函数，用于返回完整结果
     */
    void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter, 
                                  Consumer<JSONObject> incrementalCallback,
                                  Consumer<JSONObject> resultCallback);

    /**
     * 获取DeepSeek分析结果流，带增量更新和完整结果回调，支持繁体转换
     * @param chartData 星盘数据
     * @param emitter SSE发射器
     * @param incrementalCallback 每收到一段内容时的回调，用于实时广播增量更新
     * @param resultCallback 分析完成后的回调函数，用于返回完整结果
     * @param needTraditionalChinese 是否需要繁体转换
     */
    void getDeepSeekAnalysisStream(JSONObject chartData, SseEmitter emitter, 
                                  Consumer<JSONObject> incrementalCallback,
                                  Consumer<JSONObject> resultCallback,
                                  boolean needTraditionalChinese);

    /**
     * 根据ID获取星体信息
     * @param starInfoByIdRequest 根据id获取星体信息请求
     * @return 星体信息
     */
    Result getStarInfoById(StarInfoByIdRequest starInfoByIdRequest);
}
